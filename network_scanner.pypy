from flask import Flask, render_template
import nmap
from mac_vendor_lookup import MacLookup

app = Flask(__name__)
mac_lookup = MacLookup()



def get_group_by_ip(ip):
    ip_parts = list(map(int, ip.split(".")))
    last_octet = ip_parts[3]

    if 100 <= last_octet <= 164:
        return "Service Delivery"
    elif 165 <= last_octet <= 199:
        return "Help Desk"
    elif 200 <= last_octet <= 215:
        return "Help Desk"
    else:
        return "Other"


def scan_network():
    try:
        nm = nmap.PortScanner(nmap_search_path=['C:\\Program Files (x86)\\Nmap\\nmap.exe'])
        nm.scan(hosts='***********-250', arguments='-sn -R')

        devices = []
        for host in nm.all_hosts():
            try:
                ip = host
                hostname = nm[host].hostname()
                mac = nm[host]['addresses'].get('mac', 'N/A')

                if not hostname:
                    hostname = "Unknown"

                if hostname.endswith('.comsys.local'):
                    hostname = hostname.replace('.comsys.local', '')

                try:
                    vendor = mac_lookup.lookup(mac) if mac != 'N/A' else 'N/A'
                except Exception:
                    vendor = 'Unknown'

                group = get_group_by_ip(ip)

                devices.append({
                    'ip': ip,
                    'hostname': hostname,
                    'mac': mac,
                    'vendor': vendor,
                    'group': group
                })
            except Exception as e:
                print(f"Error processing host {host}: {str(e)}")
                continue

        devices.sort(key=lambda x: (x['group'], tuple(map(int, x['ip'].split('.')))))
        return devices
    except Exception as e:
        print(f"Scan error: {str(e)}")
        return []


@app.route('/')
def index():
    devices = scan_network()
    total_devices = len(devices)
    return render_template('index.html', devices=devices, total_devices=total_devices)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')
