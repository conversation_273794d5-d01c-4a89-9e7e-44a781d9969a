from flask import Flask, render_template, request, session, redirect, url_for, flash
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import socket
import json
import os
import hashlib
import hmac
import secrets
import ipaddress
import struct
import time
import threading
from datetime import datetime, date, timedelta
from collections import defaultdict
from functools import wraps
import logging
from cryptography.fernet import Fernet
import subprocess
import sys

# Configure secure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('network_scanner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Security Configuration
class SecurityConfig:
    SECRET_KEY = os.environ.get('SECRET_KEY', secrets.token_hex(32))
    CACHE_FILE = "device_cache.enc"
    SESSION_TIMEOUT = 3600  # 1 hour
    MAX_REQUESTS_PER_MINUTE = 10
    ALLOWED_IP_RANGES = ['***********/24', '10.0.0.0/8', '**********/12']
    SCAN_IP_RANGE = '************-217'
    ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY', Fernet.generate_key())

# Performance Configuration
class PerformanceConfig:
    MAX_WORKERS = 50  # Increased from 20 to 50 for faster scanning
    PING_TIMEOUT = 1  # Reduced from 3 seconds to 1 second
    DNS_TIMEOUT = 2   # Reduced from 5 seconds to 2 seconds
    SOCKET_TIMEOUT = 0.5  # Fast socket check timeout
    CACHE_FRESH_MINUTES = 5  # Consider cache fresh for 5 minutes
    COMMON_PORTS = [80, 443, 22, 21, 23, 25, 53, 135, 139, 445]  # Ports to check for socket ping

app.config['SECRET_KEY'] = SecurityConfig.SECRET_KEY
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(seconds=SecurityConfig.SESSION_TIMEOUT)

# Rate limiting
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["100 per hour"]
)


# Initialize encryption
cipher_suite = Fernet(SecurityConfig.ENCRYPTION_KEY)

class NetworkSecurityValidator:
    @staticmethod
    def validate_ip_range(ip_range):
        """Validate that IP range is within allowed networks"""
        try:
            start_ip, end_ip = ip_range.split('-')
            start_ip = start_ip.strip()
            end_ip = f"{'.'.join(start_ip.split('.')[:-1])}.{end_ip.strip()}"
            
            for allowed_range in SecurityConfig.ALLOWED_IP_RANGES:
                network = ipaddress.IPv4Network(allowed_range, strict=False)
                if (ipaddress.IPv4Address(start_ip) in network and 
                    ipaddress.IPv4Address(end_ip) in network):
                    return True
            return False
        except Exception as e:
            logger.error(f"IP validation error: {e}")
            return False
    
    @staticmethod
    def validate_ip(ip):
        """Validate single IP address"""
        try:
            ip_obj = ipaddress.IPv4Address(ip)
            for allowed_range in SecurityConfig.ALLOWED_IP_RANGES:
                if ip_obj in ipaddress.IPv4Network(allowed_range, strict=False):
                    return True
            return False
        except Exception:
            return False

class SecureCache:
    def __init__(self, cache_file):
        self.cache_file = cache_file
        self.lock = threading.Lock()
    
    def _encrypt_data(self, data):
        """Encrypt cache data"""
        json_data = json.dumps(data).encode()
        return cipher_suite.encrypt(json_data)
    
    def _decrypt_data(self, encrypted_data):
        """Decrypt cache data"""
        try:
            decrypted_data = cipher_suite.decrypt(encrypted_data)
            return json.loads(decrypted_data.decode())
        except Exception as e:
            logger.error(f"Cache decryption error: {e}")
            return {}
    
    def load(self):
        """Load encrypted cache"""
        with self.lock:
            if os.path.exists(self.cache_file):
                try:
                    with open(self.cache_file, 'rb') as f:
                        encrypted_data = f.read()
                    
                    cache = self._decrypt_data(encrypted_data)
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(self.cache_file)).date()
                    
                    if file_mtime < date.today():
                        os.remove(self.cache_file)
                        logger.info("Cache expired and removed")
                        return {}
                    
                    logger.info("Cache loaded successfully")
                    return cache
                except Exception as e:
                    logger.error(f"Cache load error: {e}")
                    return {}
            return {}
    
    def save(self, cache):
        """Save encrypted cache"""
        with self.lock:
            try:
                encrypted_data = self._encrypt_data(cache)
                temp_file = f"{self.cache_file}.tmp"
                
                with open(temp_file, 'wb') as f:
                    f.write(encrypted_data)
                
                os.replace(temp_file, self.cache_file)
                os.chmod(self.cache_file, 0o600)  # Restrict file permissions
                logger.info("Cache saved successfully")
            except Exception as e:
                logger.error(f"Cache save error: {e}")

class SecureNetworkScanner:
    def __init__(self):
        self.cache = SecureCache(SecurityConfig.CACHE_FILE)
        self.validator = NetworkSecurityValidator()

    def _validate_hostname(self, hostname):
        """Validate hostname format"""
        if not hostname or len(hostname) > 255:
            return False
        allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-')
        return all(c in allowed_chars for c in hostname)

    def _safe_reverse_dns(self, ip):
        """Optimized reverse DNS lookup with shorter timeout"""
        try:
            if not self.validator.validate_ip(ip):
                return "Unknown"

            old_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(PerformanceConfig.DNS_TIMEOUT)

            try:
                hostname = socket.gethostbyaddr(ip)[0]

                if not self._validate_hostname(hostname):
                    return "Unknown"

                if hostname.endswith('.comsys.local'):
                    hostname = hostname.replace('.comsys.local', '')

                return hostname
            finally:
                socket.setdefaulttimeout(old_timeout)

        except (socket.herror, socket.gaierror, socket.timeout) as e:
            logger.debug(f"DNS lookup failed for {ip}: {e}")
            return "Unknown"
        except Exception as e:
            logger.error(f"Unexpected DNS error for {ip}: {e}")
            return "Unknown"

    def _fast_ping_host(self, ip):
        """Optimized ping implementation with shorter timeout"""
        try:
            if not self.validator.validate_ip(ip):
                return False

            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-w', str(PerformanceConfig.PING_TIMEOUT * 1000), ip]
            else:
                cmd = ['ping', '-c', '1', '-W', str(PerformanceConfig.PING_TIMEOUT), ip]

            result = subprocess.run(
                cmd,
                capture_output=True,
                timeout=PerformanceConfig.PING_TIMEOUT + 1,
                text=True
            )

            return result.returncode == 0

        except subprocess.TimeoutExpired:
            logger.debug(f"Ping timeout for {ip}")
            return False
        except Exception as e:
            logger.error(f"Ping error for {ip}: {e}")
            return False

    def _socket_ping_host(self, ip, ports=None, timeout=None):
        """Optimized socket-based connectivity check with multiple ports"""
        if ports is None:
            ports = PerformanceConfig.COMMON_PORTS[:3]  # Check first 3 common ports
        if timeout is None:
            timeout = PerformanceConfig.SOCKET_TIMEOUT

        try:
            if not self.validator.validate_ip(ip):
                return False

            # Try multiple ports quickly
            for port in ports:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(timeout)
                    result = sock.connect_ex((ip, port))
                    sock.close()
                    if result == 0:
                        return True
                except Exception:
                    continue
            return False
        except Exception:
            return False
    
    def _get_group_by_ip(self, ip):
        """Categorize device by IP address"""
        try:
            ip_parts = ip.split(".")
            last_octet = int(ip_parts[3])
            
            if 100 <= last_octet <= 162:
                return "Service Delivery"
            elif 163 <= last_octet <= 206:
                return "Implementation"
            elif 207 <= last_octet <= 217:
                return "Help Desk"
            elif 0 <= last_octet <= 50:
                return "Servers"
            else:
                return "Other"
        except (ValueError, IndexError):
            return "Other"
    
    def scan_network(self, ip_range=None, max_age_hours=24, force_refresh=False):
        """Optimized network scanning with smart caching and parallel processing"""
        if ip_range is None:
            ip_range = SecurityConfig.SCAN_IP_RANGE

        if not self.validator.validate_ip_range(ip_range):
            logger.error("Invalid IP range provided")
            return []

        logger.info(f"Starting optimized network scan for range: {ip_range}")
        start_time = time.time()

        try:
            base_ip, end_range = ip_range.split('-')
            base_parts = base_ip.split('.')
            start_octet = int(base_parts[3])
            end_octet = int(end_range)

            devices = []
            cache = self.cache.load()
            now = datetime.now()
            now_str = now.strftime("%Y-%m-%d %H:%M:%S")
            cutoff_time = now - timedelta(hours=max_age_hours)
            fresh_cache_cutoff = now - timedelta(minutes=PerformanceConfig.CACHE_FRESH_MINUTES)

            # Clean expired entries
            expired_ips = []
            for ip in list(cache.keys()):
                last_seen_str = cache[ip].get('last_seen')
                if last_seen_str:
                    try:
                        last_seen = datetime.strptime(last_seen_str, "%Y-%m-%d %H:%M:%S")
                        if last_seen < cutoff_time:
                            expired_ips.append(ip)
                    except ValueError:
                        expired_ips.append(ip)

            for ip in expired_ips:
                del cache[ip]
                logger.debug(f"Removed inactive device {ip} from cache")

            # Separate IPs into cached (fresh) and need-to-scan
            ips_to_scan = []
            fresh_devices = []

            for octet in range(start_octet, end_octet + 1):
                ip = f"{'.'.join(base_parts[:3])}.{octet}"
                cached_data = cache.get(ip)

                if not force_refresh and cached_data and cached_data.get('last_seen'):
                    try:
                        last_seen = datetime.strptime(cached_data['last_seen'], "%Y-%m-%d %H:%M:%S")
                        if last_seen > fresh_cache_cutoff:
                            # Use fresh cached data
                            device = {
                                'ip': ip,
                                'hostname': cached_data.get('hostname', 'Unknown'),
                                'group': self._get_group_by_ip(ip),
                                'first_seen': cached_data.get('first_seen', now_str)
                            }
                            fresh_devices.append(device)
                            logger.debug(f"Using cached data for {ip}")
                            continue
                    except ValueError:
                        pass

                ips_to_scan.append(octet)

            logger.info(f"Using {len(fresh_devices)} cached devices, scanning {len(ips_to_scan)} IPs")

            from concurrent.futures import ThreadPoolExecutor, as_completed

            def scan_single_ip(octet):
                ip = f"{'.'.join(base_parts[:3])}.{octet}"

                try:
                    # Try fast socket ping first, fallback to system ping
                    is_alive = self._socket_ping_host(ip) or self._fast_ping_host(ip)

                    if not is_alive:
                        return None

                    # Check cache for hostname first
                    cached_data = cache.get(ip, {})
                    cached_hostname = cached_data.get('hostname')

                    # Only do DNS lookup if we don't have a cached hostname or it's "Unknown"
                    if not cached_hostname or cached_hostname == "Unknown":
                        hostname = self._safe_reverse_dns(ip)
                    else:
                        hostname = cached_hostname

                    group = self._get_group_by_ip(ip)
                    first_seen = cached_data.get('first_seen', now_str)

                    cache[ip] = {
                        'hostname': hostname,
                        'first_seen': first_seen,
                        'last_seen': now_str
                    }

                    device = {
                        'ip': ip,
                        'hostname': hostname,
                        'group': group,
                        'first_seen': first_seen
                    }

                    logger.debug(f"Device discovered: {ip} ({hostname}) - {group}")
                    return device

                except Exception as e:
                    logger.error(f"Error scanning {ip}: {e}")
                    return None

            # Use more threads for faster scanning
            max_workers = min(PerformanceConfig.MAX_WORKERS, len(ips_to_scan)) if ips_to_scan else 1

            if ips_to_scan:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_ip = {
                        executor.submit(scan_single_ip, octet): octet
                        for octet in ips_to_scan
                    }

                    for future in as_completed(future_to_ip):
                        device = future.result()
                        if device:
                            devices.append(device)

            # Combine fresh cached devices with newly scanned devices
            devices.extend(fresh_devices)

            self.cache.save(cache)

            group_order = {
                "Service Delivery": 1,
                "Implementation": 2,
                "Help Desk": 3,
                "Other": 4,
                "Servers": 5
            }

            devices.sort(key=lambda x: (
                group_order.get(x['group'], 99),
                tuple(map(int, x['ip'].split('.')))
            ))

            scan_time = time.time() - start_time
            logger.info(f"Optimized scan completed in {scan_time:.2f}s. Found {len(devices)} active devices")
            return devices

        except Exception as e:
            logger.error(f"Network scan error: {e}")
            return []



# Authentication decorator
def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('authenticated'):
            return redirect(url_for('login'))
        
        # Check session timeout
        if session.get('login_time'):
            login_time = datetime.fromisoformat(session['login_time'])
            if datetime.now() - login_time > timedelta(seconds=SecurityConfig.SESSION_TIMEOUT):
                session.clear()
                flash('Session expired. Please login again.', 'warning')
                return redirect(url_for('login'))
        
        return f(*args, **kwargs)
    return decorated_function

# Initialize scanner
scanner = SecureNetworkScanner()

@app.route('/login', methods=['GET', 'POST'])
@limiter.limit("5 per minute")
def login():
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        
        # Simple authentication (replace with proper auth system)
        expected_username = os.environ.get('SCANNER_USERNAME', 'admin')
        expected_password = os.environ.get('SCANNER_PASSWORD', '123')
        
        if (username == expected_username and 
            hmac.compare_digest(password, expected_password)):
            
            session['authenticated'] = True
            session['username'] = username
            session['login_time'] = datetime.now().isoformat()
            session.permanent = True
            
            logger.info(f"Successful login for user: {username} from {request.remote_addr}")
            return redirect(url_for('index'))
        else:
            logger.warning(f"Failed login attempt for user: {username} from {request.remote_addr}")
            flash('Invalid credentials', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    username = session.get('username', 'unknown')
    session.clear()
    logger.info(f"User {username} logged out")
    flash('You have been logged out', 'info')
    return redirect(url_for('login'))

@app.route('/api/quick-status')
@require_auth
@limiter.limit("30 per minute")
def quick_status():
    """Quick API endpoint to get cached device count and last scan time"""
    try:
        cache = scanner.cache.load()
        now = datetime.now()
        cutoff_time = now - timedelta(minutes=PerformanceConfig.CACHE_FRESH_MINUTES)

        fresh_devices = 0
        last_scan_time = None

        for data in cache.values():
            if data.get('last_seen'):
                try:
                    last_seen = datetime.strptime(data['last_seen'], "%Y-%m-%d %H:%M:%S")
                    if last_seen > cutoff_time:
                        fresh_devices += 1
                    if not last_scan_time or last_seen > last_scan_time:
                        last_scan_time = last_seen
                except ValueError:
                    continue

        return {
            'fresh_devices': fresh_devices,
            'total_cached': len(cache),
            'last_scan': last_scan_time.strftime("%Y-%m-%d %H:%M:%S") if last_scan_time else None,
            'cache_age_minutes': PerformanceConfig.CACHE_FRESH_MINUTES
        }
    except Exception as e:
        logger.error(f"Error in quick status: {e}")
        return {'error': 'Failed to get status'}, 500

@app.route('/')
@require_auth
@limiter.limit("10 per minute")
def index():
    try:
        # Check if force refresh is requested
        force_refresh = request.args.get('refresh', 'false').lower() == 'true'

        # Start timing the scan
        start_time = time.time()
        devices = scanner.scan_network(force_refresh=force_refresh)
        scan_duration = time.time() - start_time

        # Filter out devices with unknown hostnames by default
        hide_unknown = request.args.get('show_unknown', 'false').lower() != 'true'
        if hide_unknown:
            devices = [device for device in devices if device['hostname'] != 'Unknown']

        total_devices = len(devices)

        # Group devices
        grouped = defaultdict(list)
        group_order = {
            "Service Delivery": 1,
            "Implementation": 2,
            "Help Desk": 3,
            "Other": 4,
            "Servers": 5
        }

        for device in devices:
            grouped[device["group"]].append(device)

        sorted_groups = sorted(grouped.items(), key=lambda x: group_order.get(x[0], 99))

        for _, dev_list in sorted_groups:
            dev_list.sort(key=lambda d: tuple(map(int, d['ip'].split('.'))))

        logger.info(f"Dashboard accessed by {session.get('username')} - {total_devices} devices displayed in {scan_duration:.2f}s")

        # Add scan performance info to flash message
        if force_refresh:
            flash(f'Network refreshed successfully in {scan_duration:.2f} seconds', 'success')

        return render_template('index.html',
                             grouped_devices=sorted_groups,
                             total_devices=total_devices,
                             scan_duration=scan_duration)

    except Exception as e:
        logger.error(f"Error in index route: {e}")
        flash('Error occurred while scanning network', 'error')
        return render_template('index.html',
                             grouped_devices=[],
                             total_devices=0,
                             scan_duration=0)


if __name__ == '__main__':
    # Ensure templates directory exists
    if not os.path.exists('templates'):
        os.makedirs('templates')
        logger.warning("Templates directory created. Please add your HTML templates.")
    
    # Start the application
    logger.info("Starting secure network scanner application")
    app.run(debug=True, host='0.0.0.0', port=5000)

