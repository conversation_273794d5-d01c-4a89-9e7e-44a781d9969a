<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Device Scanner</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .stats {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .group-section {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .group-header {
            background: #007bff;
            color: white;
            padding: 1rem;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .logout-btn {
            background-color: #dc3545;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 4px;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        .refresh-btn {
            background-color: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
        }
        .refresh-btn:hover {
            background-color: #218838;
        }
        .no-devices {
            padding: 2rem;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Network Device Scanner</h1>
        <div>
            <a href="/?refresh=true" class="refresh-btn" title="Force full network refresh">🔄 Force Refresh</a>
            <a href="/" class="refresh-btn" title="Smart refresh using cache">⚡ Smart Refresh</a>
            <a href="/logout" class="logout-btn">Logout</a>
        </div>
    </div>

<div class="stats">
    <h3>Scan Results</h3>
    <p><strong>Total Devices Found:</strong> <span id="total-count">{{ total_devices }}</span></p>
    {% if scan_duration %}
    <p><strong>Scan Duration:</strong> <span id="scan-duration">{{ "%.2f"|format(scan_duration) }}s</span></p>
    {% endif %}
    <p><strong>Last Scan:</strong> <span id="current-time"></span></p>
    <div style="margin-top: 10px;">
        <label>
            <input type="checkbox" id="hide-unknown" checked onchange="toggleUnknownDevices()">
            Hide devices with unknown hostnames
        </label>
    </div>
    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
        💡 <strong>Performance Tips:</strong> Smart refresh uses cached data (5min fresh) for faster results. Force refresh scans all devices.
    </div>
</div>



    {% if grouped_devices %}
        {% for group_name, devices in grouped_devices %}
        <div class="group-section">
            <div class="group-header">
                {{ group_name }} ({{ devices|length }} devices)
            </div>
            <table>
                <thead>
                    <tr>
                        <th>IP Address</th>
                        <th>Hostname</th>
                        <th>First Seen</th>
                    </tr>
                </thead>
                <tbody>
                    {% for device in devices %}
                    <tr>
                        <td>{{ device.ip }}</td>
                        <td>{{ device.hostname }}</td>
                        <td>{{ device.first_seen }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endfor %}
    {% else %}
        <div class="group-section">
            <div class="no-devices">
                <h3>No devices found</h3>
                <p>No active devices were detected in the scan range.</p>
            </div>
        </div>
    {% endif %}

   <script>
    // Display current time
    document.getElementById('current-time').textContent = new Date().toLocaleString();
    
    function toggleUnknownDevices() {
        const checkbox = document.getElementById('hide-unknown');
        const groupSections = document.querySelectorAll('.group-section');
        let totalVisibleDevices = 0;
        
        groupSections.forEach(groupSection => {
            const rows = groupSection.querySelectorAll('tbody tr');
            const groupHeader = groupSection.querySelector('.group-header');
            let visibleInGroup = 0;
            
            rows.forEach(row => {
                const hostname = row.cells[1].textContent.trim();
                if (checkbox.checked && hostname === 'Unknown') {
                    row.style.display = 'none';
                } else {
                    row.style.display = '';
                    visibleInGroup++;
                }
            });
            
            // Update group header count
            const groupName = groupHeader.textContent.split(' (')[0];
            groupHeader.textContent = `${groupName} (${visibleInGroup} devices)`;
            totalVisibleDevices += visibleInGroup;
            
            // Hide entire group section if no visible devices
            if (visibleInGroup === 0) {
                groupSection.style.display = 'none';
            } else {
                groupSection.style.display = 'block';
            }
        });
        
        document.getElementById('total-count').textContent = totalVisibleDevices;
    }
    
    // Apply filter on page load (since checkbox is checked by default)
    window.addEventListener('load', function() {
        toggleUnknownDevices();
    });
    
    // Auto-refresh every 5 minutes
    setTimeout(function() {
        window.location.reload();
    }, 300000);
</script>


</body>
</html>
