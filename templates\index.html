<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Device Scanner</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header h1 i {
            color: #667eea;
            font-size: 1.8rem;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 65, 108, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            color: #2c3e50;
            font-size: 1.1rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .filter-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-section label {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #2c3e50;
            font-weight: 500;
            cursor: pointer;
        }

        .filter-section input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .group-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            margin-bottom: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .group-section:hover {
            transform: translateY(-3px);
        }

        .group-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.2rem 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .group-header i {
            font-size: 1.2rem;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #2c3e50;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 2px solid #dee2e6;
        }

        td {
            padding: 15px;
            border-bottom: 1px solid #f1f3f4;
            color: #2c3e50;
            font-size: 0.95rem;
        }

        tr:hover {
            background: linear-gradient(135deg, #f8f9ff, #f0f4ff);
        }

        .ip-address {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #667eea;
        }

        .hostname {
            font-weight: 500;
        }

        .hostname.unknown {
            color: #95a5a6;
            font-style: italic;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .no-devices {
            padding: 3rem;
            text-align: center;
            color: #7f8c8d;
        }

        .no-devices i {
            font-size: 4rem;
            color: #bdc3c7;
            margin-bottom: 1rem;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
            color: #667eea;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .performance-tip {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border-left: 4px solid #27ae60;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
            color: #2c3e50;
        }

        .performance-tip i {
            color: #27ae60;
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filter-section {
                justify-content: center;
            }

            .table-container {
                font-size: 0.85rem;
            }

            th, td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-network-wired"></i>
                Network Device Scanner
            </h1>
            <div class="header-actions">
                <a href="/?refresh=true" class="btn btn-primary" title="Force full network refresh">
                    <i class="fas fa-sync-alt"></i> Force Refresh
                </a>
                <a href="/" class="btn btn-success" title="Smart refresh using cache">
                    <i class="fas fa-bolt"></i> Smart Refresh
                </a>
                <a href="/logout" class="btn btn-danger">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3><i class="fas fa-desktop"></i> Total Devices</h3>
                <div class="stat-value" id="total-count">{{ total_devices }}</div>
                <div class="stat-label">Active devices found</div>
            </div>

            {% if scan_duration %}
            <div class="stat-card">
                <h3><i class="fas fa-stopwatch"></i> Scan Duration</h3>
                <div class="stat-value" id="scan-duration">{{ "%.2f"|format(scan_duration) }}s</div>
                <div class="stat-label">Last scan performance</div>
            </div>
            {% endif %}

            <!--div class="stat-card">
                <h3><i class="fas fa-clock"></i> Last Updated</h3>
                <div class="stat-value" id="current-time" style="font-size: 1.2rem;"></div>
                <div class="stat-label">Real-time status</div>
            </div-->
        </div>

        <!-- Controls -->
        <!--div class="controls">
            <div class="filter-section">
                <label>
                    <input type="checkbox" id="hide-unknown" checked onchange="toggleUnknownDevices()">
                    <i class="fas fa-eye-slash"></i>
                    Hide devices with unknown hostnames
                </label>

                <label>
                    <input type="checkbox" id="auto-refresh" checked onchange="toggleAutoRefresh()">
                    <i class="fas fa-sync"></i>
                    Auto-refresh (5 minutes)
                </label>
            </div>

            <div class="performance-tip">
                <i class="fas fa-lightbulb"></i>
                <strong>Performance Tips:</strong> Smart refresh uses cached data (5min fresh) for faster results. Force refresh scans all devices.
            </div>
        </div-->

        <!-- Loading indicator -->
        <div class="loading" id="loading">
            <i class="fas fa-spinner"></i>
            <p>Scanning network devices...</p>
        </div>


        <!-- Device Groups -->
        {% if grouped_devices %}
            {% for group_name, devices in grouped_devices %}
            <div class="group-section" data-group="{{ group_name }}">
                <div class="group-header">
                    {% if group_name == "Service Delivery" %}
                        <i class="fas fa-users"></i>
                    {% elif group_name == "Implementation" %}
                        <i class="fas fa-cogs"></i>
                    {% elif group_name == "Help Desk" %}
                        <i class="fas fa-headset"></i>
                    {% elif group_name == "Servers" %}
                        <i class="fas fa-server"></i>
                    {% else %}
                        <i class="fas fa-desktop"></i>
                    {% endif %}
                    <span class="group-title">{{ group_name }}</span>
                    <span class="group-count">({{ devices|length }} devices)</span>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th><i class="fas fa-network-wired"></i> IP Address</th>
                                <th><i class="fas fa-tag"></i> Hostname</th>
                                <th><i class="fas fa-calendar-alt"></i> First Seen</th>
                                <th><i class="fas fa-signal"></i> Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for device in devices %}
                            <tr data-hostname="{{ device.hostname }}">
                                <td class="ip-address">{{ device.ip }}</td>
                                <td class="hostname {% if device.hostname == 'Unknown' %}unknown{% endif %}">
                                    {% if device.hostname == 'Unknown' %}
                                        <i class="fas fa-question-circle"></i>
                                    {% else %}
                                        <i class="fas fa-desktop"></i>
                                    {% endif %}
                                    {{ device.hostname }}
                                </td>
                                <td>
                                    <i class="fas fa-clock"></i>
                                    {{ device.first_seen }}
                                </td>
                                <td>
                                    <span class="status-indicator"></span>
                                    Online
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="group-section">
                <div class="no-devices">
                    <i class="fas fa-search"></i>
                    <h3>No devices found</h3>
                    <p>No active devices were detected in the scan range.</p>
                    <a href="/?refresh=true" class="btn btn-primary" style="margin-top: 15px;">
                        <i class="fas fa-sync-alt"></i> Try Force Refresh
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <script>
        let autoRefreshTimer = null;

        // Update current time
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        // Update time every second
        updateCurrentTime();
        setInterval(updateCurrentTime, 1000);

        function toggleUnknownDevices() {
            const checkbox = document.getElementById('hide-unknown');
            const groupSections = document.querySelectorAll('.group-section[data-group]');
            let totalVisibleDevices = 0;

            groupSections.forEach(groupSection => {
                const rows = groupSection.querySelectorAll('tbody tr');
                const groupCount = groupSection.querySelector('.group-count');
                let visibleInGroup = 0;

                rows.forEach(row => {
                    const hostname = row.getAttribute('data-hostname');
                    if (checkbox.checked && hostname === 'Unknown') {
                        row.style.display = 'none';
                    } else {
                        row.style.display = '';
                        visibleInGroup++;
                    }
                });

                // Update group header count
                if (groupCount) {
                    groupCount.textContent = `(${visibleInGroup} devices)`;
                }
                totalVisibleDevices += visibleInGroup;

                // Hide entire group section if no visible devices
                if (visibleInGroup === 0) {
                    groupSection.style.display = 'none';
                } else {
                    groupSection.style.display = 'block';
                }
            });

            document.getElementById('total-count').textContent = totalVisibleDevices;
        }

        function toggleAutoRefresh() {
            const checkbox = document.getElementById('auto-refresh');

            if (autoRefreshTimer) {
                clearTimeout(autoRefreshTimer);
                autoRefreshTimer = null;
            }

            if (checkbox.checked) {
                autoRefreshTimer = setTimeout(function() {
                    showLoading();
                    window.location.reload();
                }, 3600000); // 1 minutes = 60.000
            }
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        // Apply filters on page load
        window.addEventListener('load', function() {
            toggleUnknownDevices();
            toggleAutoRefresh();
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // H key to toggle hide unknown
            if (e.key === 'h' || e.key === 'H') {
                if (e.target.tagName !== 'INPUT') {
                    const checkbox = document.getElementById('hide-unknown');
                    checkbox.checked = !checkbox.checked;
                    toggleUnknownDevices();
                }
            }
        });

        // Add visual feedback for button clicks
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.href && (this.href.includes('refresh') || this.href === window.location.origin + '/')) {
                    showLoading();
                }
            });
        });
    </script>


</body>
</html>
